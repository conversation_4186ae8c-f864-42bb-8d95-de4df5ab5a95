"use client";

import { useAuth } from "@/contexts/auth-context";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useState, useRef, useCallback } from "react";
import { UserRole } from "@/types/team";
import { toast } from "sonner";
import { SYSTEM_PAGES } from "@/types/permissions";
import { User } from "firebase/auth";

interface RouteGuardProps {
  children: React.ReactNode;
}

export function RouteGuard({ children }: RouteGuardProps) {
  const { user, loading, profileLoading, userProfile, isAdmin, isPageHidden, permissionsLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const loadingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastPathRef = useRef<string>(pathname);
  const lastUserStateRef = useRef<{ user: User | null; loading: boolean }>({ user: null, loading: true });

  // Rotas públicas que não precisam de autenticação
  const publicRoutes = ['/login', '/register', '/forgot-password', '/api/auth'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Verificar se é a rota de completar perfil
  const isCompleteProfileRoute = pathname.startsWith('/complete-profile');

  // Rotas administrativas que requerem papel de admin
  const adminRoutes = ['/admin'];
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));

  // Rotas restritas por equipa (apenas membros da equipa podem acessar)
  const teamRestrictedRoutes = ['/registos', '/condutores', '/identificacoes', '/relatorios'];
  const isTeamRestrictedRoute = teamRestrictedRoutes.some(route => pathname.startsWith(route));

  // Rotas compartilhadas (todos os usuários autenticados podem acessar)
  const sharedRoutes = [
    '/dashboard',
    '/formularios',
    '/textos',
    '/nips',
    '/contactos',
    '/estabelecimentos',
    '/viaturas',
    '/account'
  ];
  const isSharedRoute = sharedRoutes.some(route => pathname.startsWith(route));

  // Verificar se o usuário tem permissão para acessar a rota atual
  const hasRoutePermission = useCallback(() => {
    // Rotas públicas não precisam de verificação de permissão
    if (isPublicRoute) return true;

    // A rota de completar perfil é acessível para qualquer usuário autenticado
    if (isCompleteProfileRoute) return true;

    // Rotas administrativas requerem papel de admin
    if (isAdminRoute) return isAdmin;

    // Verificar se a página está oculta para o usuário
    // Primeiro, encontrar a página correspondente à rota atual
    const currentPage = SYSTEM_PAGES.find(page => pathname.startsWith(page.url));
    if (currentPage && isPageHidden(currentPage.id)) {
      return false;
    }

    // Rotas restritas por equipa requerem pelo menos papel de membro de equipa
    if (isTeamRestrictedRoute) {
      return userProfile?.role === UserRole.ADMIN ||
             userProfile?.role === UserRole.TEAM_LEADER ||
             userProfile?.role === UserRole.TEAM_MEMBER;
    }

    // Rotas compartilhadas podem ser acessadas por qualquer usuário autenticado
    if (isSharedRoute) return true;

    // Para outras rotas, verificar se o usuário tem algum papel definido
    return userProfile?.role !== undefined;
  }, [isPublicRoute, isCompleteProfileRoute, isAdminRoute, isAdmin, pathname, isPageHidden, isTeamRestrictedRoute, userProfile?.role, isSharedRoute]);

  // Função para verificar se é uma sessão anônima
  const isLikelyAnonymousSession = () => {
    try {
      const hasLocalStorage = localStorage.getItem('firebase:authUser');
      const hasSessionStorage = sessionStorage.getItem('firebase:authUser');
      return !hasLocalStorage && !hasSessionStorage;
    } catch {
      // Se não conseguir acessar localStorage/sessionStorage, assumir que é anônimo
      return true;
    }
  };

  // Função para verificar se estamos em processo de redirecionamento pós-login
  const isPostLoginRedirect = () => {
    try {
      return sessionStorage.getItem('postLoginRedirect') === 'true';
    } catch {
      return false;
    }
  };

  // Função para obter mensagem de carregamento contextual
  const getLoadingMessage = () => {
    if (loading) return "Verificando autenticação...";
    if (profileLoading) return "Carregando perfil...";
    if (permissionsLoading) return "Verificando permissões...";
    return "Carregando...";
  };

  // Limpar timeouts quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, []);

  // Detectar sessões anônimas e redirecionar imediatamente
  useEffect(() => {
    // Verificar se é uma sessão claramente anônima tentando acessar rota protegida
    if (!isPublicRoute && isLikelyAnonymousSession()) {
      setIsRedirecting(true);
      router.push("/login");
    }
  }, [pathname, isPublicRoute, router]);

  // Implementar timeout de carregamento para evitar loading infinito
  useEffect(() => {
    if (loading || profileLoading || permissionsLoading) {
      // Limpar timeout anterior se existir
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }

      // Definir novo timeout de 5 segundos
      loadingTimeoutRef.current = setTimeout(() => {
        if (loading || profileLoading || permissionsLoading) {
          console.warn("Loading timeout reached, redirecting to login");
          setLoadingTimeout(true);
          setIsRedirecting(true);
          router.push("/login");
        }
      }, 5000);
    } else {
      // Limpar timeout se carregamento terminou
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
      setLoadingTimeout(false);
    }

    return () => {
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
      }
    };
  }, [loading, profileLoading, permissionsLoading, router]);

  // Detectar mudanças de rota
  useEffect(() => {
    if (pathname !== lastPathRef.current) {
      lastPathRef.current = pathname;
      setIsRedirecting(false);
      setLoadingTimeout(false);

      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
        redirectTimeoutRef.current = null;
      }
      if (loadingTimeoutRef.current) {
        clearTimeout(loadingTimeoutRef.current);
        loadingTimeoutRef.current = null;
      }
    }
  }, [pathname]);

  // Detectar mudanças recentes no estado de autenticação
  useEffect(() => {
    const currentState = { user, loading };
    const lastState = lastUserStateRef.current;

    // Detectar se o usuário acabou de fazer login (mudou de null para user e loading terminou)
    const justLoggedIn = !lastState.user && currentState.user && !currentState.loading;

    // Atualizar referência do estado anterior
    lastUserStateRef.current = currentState;

    // Se acabou de fazer login ou estamos em processo de redirecionamento pós-login, aguardar
    if ((justLoggedIn || isPostLoginRedirect()) && isPublicRoute) {
      console.log("Recent login or post-login redirect detected, allowing AuthContext to handle redirect");
      return; // Não fazer nada, deixar o AuthContext lidar com o redirecionamento
    }
  }, [user, loading, isPublicRoute]);

  // Verificação progressiva de autenticação - Primeira fase: Auth rápida
  useEffect(() => {
    // Evitar redirecionamentos em cascata ou se já estiver redirecionando
    if (isRedirecting || loadingTimeout) return;

    // Verificar se acabou de fazer login recentemente
    const currentState = { user, loading };
    const lastState = lastUserStateRef.current;
    const justLoggedIn = !lastState.user && currentState.user && !currentState.loading;

    // Se acabou de fazer login, aguardar mais tempo para evitar conflito
    if (justLoggedIn && isPublicRoute) {
      console.log("Delaying RouteGuard redirect due to recent login");
      redirectTimeoutRef.current = setTimeout(() => {
        if (isPublicRoute && user) {
          setIsRedirecting(true);
          router.push("/dashboard");
        }
      }, 1500); // Aguardar 1.5s para dar tempo ao AuthContext
      return;
    }

    // Verificação rápida: usuário claramente não autenticado
    if (!loading && !user && !isPublicRoute) {
      setIsRedirecting(true);
      redirectTimeoutRef.current = setTimeout(() => {
        router.push("/login");
      }, 300);
    }
    // Usuário autenticado tentando acessar rota pública (caso normal, não pós-login)
    else if (!loading && !profileLoading && user && isPublicRoute && pathname !== '/forgot-password' && !justLoggedIn) {
      setIsRedirecting(true);
      redirectTimeoutRef.current = setTimeout(() => {
        router.push("/dashboard");
      }, 300);
    }
  }, [user, loading, profileLoading, isPublicRoute, pathname, router, isRedirecting, loadingTimeout]);

  // Verificação progressiva de autenticação - Segunda fase: Permissões
  useEffect(() => {
    // Evitar redirecionamentos em cascata
    if (isRedirecting || loadingTimeout) return;

    // Verificar permissões apenas após todos os carregamentos terminarem
    if (!loading && !profileLoading && !permissionsLoading && user && !isPublicRoute) {
      if (!hasRoutePermission()) {
        setIsRedirecting(true);

        toast.error("Acesso negado", {
          description: "Você não tem permissão para acessar esta página.",
        });

        redirectTimeoutRef.current = setTimeout(() => {
          router.push("/dashboard");
        }, 500);
      }
    }
  }, [user, loading, profileLoading, permissionsLoading, isPublicRoute, pathname, router, isRedirecting, loadingTimeout, hasRoutePermission]);

  // Mostrar um indicador de carregamento enquanto verifica a autenticação
  if ((loading || profileLoading || permissionsLoading) && !loadingTimeout) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">{getLoadingMessage()}</p>
          <p className="mt-2 text-xs text-muted-foreground/70">
            Se esta tela persistir, você será redirecionado automaticamente
          </p>
        </div>
      </div>
    );
  }

  // Se houve timeout de carregamento, mostrar mensagem específica
  if (loadingTimeout) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Redirecionando para o login...</p>
          <p className="mt-2 text-xs text-muted-foreground/70">
            Tempo limite de carregamento atingido
          </p>
        </div>
      </div>
    );
  }

  // Renderizar o conteúdo apenas se o usuário tiver permissão
  if (isPublicRoute || (user && hasRoutePermission())) {
    return <>{children}</>;
  }

  // Caso contrário, mostrar uma tela de carregamento enquanto redireciona
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-muted-foreground">Redirecionando...</p>
      </div>
    </div>
  );
}
