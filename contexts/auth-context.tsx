"use client";

import React, { createContext, useContext, useEffect, useState, useCallback } from "react";
import {
  User,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
} from "firebase/auth";
import { auth, db } from "@/lib/firebase";
import { doc, getDoc, setDoc, serverTimestamp } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { showSuccessToast, showErrorToast, dismissToast } from "@/lib/toast-utils";
import { UserRole } from "@/types/team";
import { getUserTeam } from "@/services/teams-service";
import { PagePermission, shouldHidePage } from "@/types/permissions";
import { getAllUserPermissions } from "@/services/permissions-service";

interface UserProfile {
  fullName?: string;
  registrationNumber?: string;
  category?: string;
  profileCompleted?: boolean;
  role?: UserRole;
  teamId?: string;
  canEditProfile?: boolean; // Indica se o utilizador pode editar seu próprio perfil
}

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  profileLoading: boolean;
  signIn: (email: string, password: string, loadingToastId?: string | number) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserProfile: (profile: UserProfile) => Promise<void>;
  isAdmin: boolean;
  isTeamLeader: boolean;
  isTeamMember: boolean;
  userTeamId: string | undefined;
  canEditProfile: boolean; // Indica se o utilizador pode editar seu próprio perfil
  pagePermissions: PagePermission[]; // Permissões de página do usuário
  permissionsLoading: boolean; // Indica se as permissões estão sendo carregadas
  isPageHidden: (pageId: string) => boolean; // Função para verificar se uma página deve ser ocultada
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(true);
  const [userTeamId, setUserTeamId] = useState<string | undefined>(undefined);
  const [pagePermissions, setPagePermissions] = useState<PagePermission[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);
  const router = useRouter();

  // Computed properties for user roles
  const isAdmin = userProfile?.role === UserRole.ADMIN;
  const isTeamLeader = userProfile?.role === UserRole.TEAM_LEADER;
  const isTeamMember = userProfile?.role === UserRole.TEAM_MEMBER;

  // Função para verificar se uma página deve ser ocultada
  const isPageHidden = useCallback((pageId: string): boolean => {
    // Administradores sempre veem todas as páginas
    if (isAdmin) return false;

    // Verificar nas permissões do usuário
    return shouldHidePage(pageId, pagePermissions);
  }, [isAdmin, pagePermissions]);

  // Verificar se o utilizador pode editar seu próprio perfil
  // Por padrão, se não estiver definido, o utilizador pode editar seu perfil
  const canEditProfile = userProfile?.canEditProfile !== false;

  // Função para buscar as permissões do usuário
  const fetchUserPermissions = useCallback(async (userId: string, teamId?: string) => {
    setPermissionsLoading(true);
    try {
      // Obter todas as permissões do usuário (incluindo as da equipe)
      const permissions = await getAllUserPermissions(userId, teamId);
      setPagePermissions(permissions);
    } catch (error) {
      setPagePermissions([]);
    } finally {
      setPermissionsLoading(false);
    }
  }, []);

  // Função para buscar o perfil do usuário
  const fetchUserProfile = useCallback(async (userId: string) => {
    setProfileLoading(true);

    try {
      // Buscar o perfil do usuário no Firestore
      const userDocRef = doc(db, "users", userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data() as UserProfile;
        setUserProfile(userData);

        // Se o usuário tiver um papel definido, buscar informações da equipa
        if (userData.role === UserRole.TEAM_LEADER || userData.role === UserRole.TEAM_MEMBER) {
          try {
            // Se o usuário já tem teamId definido, usar esse valor diretamente
            if (userData.teamId) {
              setUserTeamId(userData.teamId);

              // Buscar permissões do usuário
              await fetchUserPermissions(userId, userData.teamId);
            } else {
              // Caso contrário, buscar a equipa do usuário
              try {
                const team = await getUserTeam(userId);
                if (team) {
                  setUserTeamId(team.id);

                  // Atualizar o perfil do usuário com o teamId
                  await setDoc(doc(db, "users", userId), {
                    teamId: team.id,
                    updatedAt: serverTimestamp(),
                  }, { merge: true });

                  // Buscar permissões do usuário
                  await fetchUserPermissions(userId, team.id);
                } else {
                  // Buscar apenas permissões do usuário
                  await fetchUserPermissions(userId);
                }
              } catch (error) {
                // Buscar apenas permissões do usuário
                await fetchUserPermissions(userId);
              }
            }
          } catch (error) {
            // Buscar apenas permissões do usuário
            await fetchUserPermissions(userId);
          }
        } else if (userData.role === UserRole.ADMIN) {
          // Administradores não precisam de permissões específicas
          setPagePermissions([]);
          setPermissionsLoading(false);
        } else {
          // Usuário sem papel definido, buscar apenas permissões do usuário
          await fetchUserPermissions(userId);
        }
      } else {
        // Se o documento não existir, definimos um perfil vazio
        setUserProfile({ profileCompleted: false });
        setPagePermissions([]);
        setPermissionsLoading(false);
      }
    } catch (error) {
      setUserProfile({ profileCompleted: false });
      setPagePermissions([]);
      setPermissionsLoading(false);
    } finally {
      setProfileLoading(false);
    }
  }, [setProfileLoading, setUserProfile, setUserTeamId, fetchUserPermissions]);

  // Efeito para configurar a autenticação
  useEffect(() => {
    let unsubscribeAuth: (() => void) | null = null;
    let isInitialized = false;

    setLoading(true);

    // Verificar se há dados de usuário em localStorage para estado inicial
    if (typeof window !== 'undefined') {
      const cachedUser = localStorage.getItem('authUser');
      if (cachedUser) {
        try {
          JSON.parse(cachedUser);
        } catch (e) {
          localStorage.removeItem('authUser');
        }
      }
    }

    // Função para lidar com mudanças no estado de autenticação
    const handleAuthStateChange = async (authUser: User | null) => {
      if (authUser) {
        // Usuário autenticado
        // Armazenar dados básicos do usuário em localStorage
        const userData = {
          uid: authUser.uid,
          email: authUser.email,
          displayName: authUser.displayName,
          photoURL: authUser.photoURL,
        };

        // Armazenar no localStorage (cliente)
        if (typeof window !== 'undefined') {
          localStorage.setItem('authUser', JSON.stringify(userData));
        }

        // Atualizar estado do usuário
        setUser(authUser);

        // Buscar perfil do usuário
        try {
          await fetchUserProfile(authUser.uid);
        } catch (error) {
          setProfileLoading(false);
        }
      } else {
        // Usuário não autenticado

        // Limpar dados do localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('authUser');
          sessionStorage.removeItem('lastCheckedPath');
        }

        // Limpar estados
        setUser(null);
        setUserProfile(null);
        setProfileLoading(false);
      }

      // Finalizar carregamento
      if (isInitialized) {
        setLoading(false);
      } else {
        isInitialized = true;
        // Pequeno atraso para garantir que o estado seja atualizado corretamente
        setTimeout(() => {
          setLoading(false);
        }, 500);
      }
    };

    // Inscrever-se para mudanças no estado de autenticação
    unsubscribeAuth = onAuthStateChanged(auth, handleAuthStateChange);

    // Limpar inscrições quando o componente for desmontado
    return () => {
      if (unsubscribeAuth) {
        console.log("Limpando inscrição de autenticação");
        unsubscribeAuth();
      }
    };
  }, [fetchUserProfile]);

  // Função para atualizar o perfil do usuário
  const updateUserProfile = async (profile: UserProfile) => {
    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    try {
      // Salvar os dados no Firestore
      await setDoc(doc(db, "users", user.uid), {
        ...profile,
        updatedAt: serverTimestamp(),
      }, { merge: true });

      // Atualiza o estado local com uma cópia profunda para garantir que as mudanças sejam detectadas
      setUserProfile(prevProfile => {
        const updatedProfile = {
          ...(prevProfile || {}),
          ...profile
        };
        return updatedProfile;
      });

      return;
    } catch (error) {
      throw error;
    }
  };

  const signIn = async (email: string, password: string, loadingToastId?: string | number) => {

    try {
      setLoading(true);

      // Autenticar com Firebase
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Primeiro, dispensar o toast de carregamento se fornecido
      if (loadingToastId) {
        dismissToast(loadingToastId);
      }

      // Aguardar um pequeno intervalo para garantir que o toast de carregamento seja removido
      await new Promise(resolve => setTimeout(resolve, 100));

      // Mostrar notificação de sucesso
      showSuccessToast("Sessão iniciada com sucesso!", {
        description: "Bem-vindo de volta!",
        icon: "👋",
      });

      // Verificar se o perfil está completo para decidir o redirecionamento
      const profile = await getDoc(doc(db, "users", user.uid));
      const profileData = profile.exists() ? profile.data() as UserProfile : null;

      // Aguardar um intervalo menor para garantir que o estado de autenticação foi processado
      // Isso ajuda a evitar problemas de corrida com o RouteGuard
      await new Promise(resolve => setTimeout(resolve, 500));

      // Limpar o caminho verificado anteriormente para permitir redirecionamento
      sessionStorage.removeItem('lastCheckedPath');

      // Marcar que estamos fazendo redirecionamento pós-login
      sessionStorage.setItem('postLoginRedirect', 'true');

      // Redirecionamento baseado no estado do perfil
      if (!profileData || !profileData.profileCompleted) {
        console.log("AuthContext: Redirecting to complete-profile");
        router.push("/complete-profile");
      } else {
        console.log("AuthContext: Redirecting to dashboard");
        router.push("/dashboard");
      }

      // Limpar a flag após um pequeno atraso
      setTimeout(() => {
        sessionStorage.removeItem('postLoginRedirect');
      }, 2000);

      // Retornar sem esperar pelo redirecionamento
      return;
    } catch (error: unknown) {
      // Tratar erros de autenticação
      let errorMessage = "Ocorreu um erro ao iniciar sessão.";
      let errorDescription = "Verifique a sua ligação à internet e tente novamente.";

      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };

        if (firebaseError.code === "auth/invalid-credential") {
          errorMessage = "Email ou palavra-passe incorretos.";
          errorDescription = "Verifique os seus dados e tente novamente.";
        } else if (firebaseError.code === "auth/user-not-found") {
          errorMessage = "Utilizador não encontrado.";
          errorDescription = "Este email não está registado no sistema.";
        } else if (firebaseError.code === "auth/wrong-password") {
          errorMessage = "Palavra-passe incorreta.";
          errorDescription = "Verifique a sua palavra-passe e tente novamente.";
        } else if (firebaseError.code === "auth/too-many-requests") {
          errorMessage = "Demasiadas tentativas.";
          errorDescription = "A sua conta foi temporariamente bloqueada. Tente novamente mais tarde.";
        }
      }

      // Dispensar o toast de carregamento se fornecido
      if (loadingToastId) {
        dismissToast(loadingToastId);
      }

      showErrorToast(errorMessage, {
        description: errorDescription,
      });
      throw error;
    } finally {
      // Definir loading como false após um atraso coordenado com o redirecionamento
      // Isso evita que o RouteGuard interfira com o redirecionamento do AuthContext
      setTimeout(() => {
        setLoading(false);
      }, 600); // Aumentado para 600ms para coordenar com o redirecionamento
    }
  };

  const signOut = async () => {
    // Definir uma flag para controlar se o componente ainda está montado
    let isMounted = true;

    // Definir um timeout para redirecionar, mesmo se houver erros
    const redirectTimeout = setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }, 1000); // Redirecionar após 1 segundo, mesmo se houver erros

    try {
      // Marcar que estamos no processo de logout para os scripts de gerenciamento de cache
      if (typeof window !== 'undefined' && window.sessionStorage) {
        sessionStorage.setItem('loggingOut', 'true');
      }

      // Limpar a UI antes de fazer logout
      if (typeof window !== 'undefined') {
        // Desativar transições para evitar problemas visuais durante o logout
        document.documentElement.classList.add('no-transitions');

        // Limpar quaisquer estados de UI que possam interferir com a página de login
        document.body.classList.remove('overflow-hidden');

        // Definir uma variável global para indicar que estamos fazendo logout
        // Isso será usado pelos componentes para evitar manipulações do DOM durante o logout
        (window as any).__LOGGING_OUT = true;
      }

      // Limpar dados do localStorage e cookies antes de fazer logout
      // Isso evita problemas de acesso a dados após o logout
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authUser');
        sessionStorage.removeItem('lastCheckedPath');
        sessionStorage.removeItem('authSessionId');

        // Limpar caches específicos relacionados à autenticação
        if ('caches' in window) {
          try {
            const cacheNames = await caches.keys();
            const authCaches = cacheNames.filter(name =>
              name.includes('auth') ||
              name.includes('user') ||
              name.includes('firebase')
            );

            await Promise.all(
              authCaches.map(cacheName => {
                return caches.delete(cacheName);
              })
            );
          } catch (cacheError) {
            // Silently handle cache errors
          }
        }
      }

      document.cookie = 'authUser=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';

      // Fazer logout do Firebase
      await firebaseSignOut(auth);

      // Verificar se o componente ainda está montado antes de mostrar o toast
      if (isMounted) {
        showSuccessToast("Sessão terminada com sucesso!", {
          description: "Até breve!",
          icon: "👋",
        });
      }

      // Usar window.location para uma navegação completa em vez de router.push
      // Isso garante que a página seja completamente recarregada
      if (typeof window !== 'undefined') {
        // Limpar o timeout, pois vamos redirecionar agora
        clearTimeout(redirectTimeout);
        // Pequeno atraso para garantir que tudo seja limpo antes de redirecionar
        setTimeout(() => {
          window.location.href = '/login';
        }, 100);
      } else {
        router.push("/login");
      }
    } catch (error) {
      // Mesmo em caso de erro, mostrar uma mensagem e redirecionar
      if (isMounted) {
        showErrorToast("Ocorreu um erro ao terminar sessão.", {
          description: "Você será redirecionado para a página de login.",
        });
      }

      // Não precisamos limpar o timeout aqui, pois queremos que o redirecionamento ocorra
    } finally {
      // Marcar o componente como desmontado
      isMounted = false;

      // Restaurar transições após um pequeno atraso
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          document.documentElement.classList.remove('no-transitions');

          // Limpar a flag de logout após o redirecionamento
          if (window.sessionStorage) {
            sessionStorage.removeItem('loggingOut');
          }
        }, 500);
      }
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      userProfile,
      loading,
      profileLoading,
      signIn,
      signOut,
      updateUserProfile,
      isAdmin,
      isTeamLeader,
      isTeamMember,
      userTeamId,
      canEditProfile,
      pagePermissions,
      permissionsLoading,
      isPageHidden
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  }
  return context;
}
